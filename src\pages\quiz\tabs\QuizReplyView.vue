<template>
  <q-page class="q-pa-md bg-transparent">
    <div class="text-h6 q-mb-md">Quiz Dashboard</div>

    <div v-if="route.params.id === 'new'" class="text-center q-my-xl text-grey">
      <q-icon name="info" size="2em" />
      <div class="text-h6 q-mt-sm">ยังไม่มีข้อมูลผลการตอบ เนื่องจากยังไม่สร้างแบบทดสอบจริง</div>
    </div>
    <div v-else>
      <div v-if="isPageLoading && !hasAnyData" class="text-center q-my-xl">
        <q-spinner-dots color="primary" size="3em" />
        <div class="text-subtitle1 q-mt-sm">Loading dashboard data...</div>
      </div>

      <q-banner v-if="dashboardStore.error" inline-actions class="text-white bg-red q-mb-md">
        An error occurred while loading dashboard data: {{ dashboardStore.error }}
        <template v-slot:action>
          <q-btn
            flat
            color="white"
            label="Retry All"
            @click="retryFetchAll"
            :loading="isPageLoading"
          />
          <q-btn flat color="white" label="Clear" @click="dashboardStore.clearError" />
        </template>
      </q-banner>

      <!-- Display content only if not initial loading OR if some data already exists -->
      <div v-if="!isPageLoading || hasAnyData">
        <div v-if="dashboardStore.assessmentMeta">
          <q-card class="q-pa-md q-mb-md custom-light-shadow" style="border-radius: 20px">
            <div class="text-subtitle1">
              ชื่อแบบทดสอบ:
              <span class="text-weight-medium">{{
                dashboardStore.assessmentMeta.assessmentName
              }}</span>
              (ID: {{ currentAssessmentId }})
            </div>
          </q-card>

          <div class="row q-gutter-md q-mb-xl q-mt-lg justify-start no-wrap">
            <q-card
              class="col-auto custom-light-shadow"
              bordered
              style="width: 32%; min-width: 300px; border-radius: 16px"
            >
              <q-card-section class="q-pa-md">
                <div class="text-body1 text-weight-medium text-left">
                  มีผู้เข้าทำแบบทดสอบนี้แล้ว (Unique Users) :
                </div>
                <br />
                <div class="text-h4 text-center" style="font-style: bold">
                  {{ dashboardStore.assessmentMeta.uniqueUsers }}
                </div>
                <div
                  class="text-body2 text-grey-7"
                  style="position: absolute; bottom: 16px; right: 16px"
                >
                  คน
                </div>
              </q-card-section>
              <q-inner-loading :showing="dashboardStore.isLoadingMeta" />
            </q-card>
            <q-card
              class="col-auto custom-light-shadow"
              bordered
              style="width: 32%; min-width: 300px; border-radius: 16px"
            >
              <q-card-section class="q-pa-md">
                <div class="text-body1 text-weight-medium text-left">
                  คะแนนสูงสุดที่ผู้ทำแบบทดสอบทำได้ :
                </div>
                <br />
                <div class="text-h4 text-center" style="font-style: bold">
                  {{ dashboardStore.assessmentMeta.highestScore }}
                </div>
                <div
                  class="text-body2 text-grey-7"
                  style="position: absolute; bottom: 16px; right: 16px"
                >
                  คะแนน
                </div>
              </q-card-section>
              <q-inner-loading :showing="dashboardStore.isLoadingMeta" />
            </q-card>
            <q-card
              class="col-auto custom-light-shadow"
              bordered
              style="width: 32%; min-width: 300px; border-radius: 16px"
            >
              <q-card-section class="q-pa-md">
                <div class="text-body1 text-weight-medium text-left">
                  คะแนนต่ำสุดที่ผู้ทำแบบทดสอบทำได้ :
                </div>
                <br />
                <div class="text-h4 text-center" style="font-style: bold">
                  {{ dashboardStore.assessmentMeta.lowestScore }}
                </div>
                <div
                  class="text-body2 text-grey-7"
                  style="position: absolute; bottom: 16px; right: 16px"
                >
                  คะแนน
                </div>
              </q-card-section>
              <q-inner-loading :showing="dashboardStore.isLoadingMeta" />
            </q-card>
          </div>
        </div>
      </div>
      <div
        v-else-if="
          !dashboardStore.isLoadingMeta && !dashboardStore.error && currentAssessmentId !== null
        "
        class="text-center q-my-md"
      >
        <p>No summary data available for this assessment, or assessment not selected.</p>
      </div>

      <q-card
        v-if="currentAssessmentId !== null"
        bordered
        elevation="4"
        class="q-pa-md"
        style="border-radius: 16px"
      >
        <q-tabs
          v-model="tab"
          dense
          class="text-black"
          active-color="accent"
          indicator-color="accent"
          align="center"
          narrow-indicator
        >
          <!-- <q-tab name="dashboard" label="ภาพรวม" /> -->
          <q-tab name="answers" label="รายข้อ" />
          <q-tab name="users" label="รายชื่อผู้เข้าร่วม" />
        </q-tabs>

        <q-tab-panels v-model="tab" animated>
          <q-tab-panel name="dashboard">
            <!-- <QuizDashboardView :assessment-id="currentAssessmentId" /> -->
          </q-tab-panel>
          <q-tab-panel name="answers">
            <QuizAnswersView :assessment-id="currentAssessmentId" />
          </q-tab-panel>
          <q-tab-panel name="users">
            <QuizTakingListView :assessment-id="currentAssessmentId" />
          </q-tab-panel>
        </q-tab-panels>
      </q-card>
      <div v-else-if="!isPageLoading && !dashboardStore.error" class="text-center q-my-xl">
        <q-icon name="help_outline" size="3em" class="text-grey" />
        <div class="text-h6 q-mt-sm text-grey">
          Please select an Assessment or ensure Assessment ID is provided.
        </div>
        <p class="text-grey">No assessment data to display.</p>
      </div>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, computed } from 'vue';
import { useRoute } from 'vue-router';
import QuizTakingListView from '../panels/QuizTakingListView.vue';
// import QuizDashboardView from '../panels/QuizDashboardView.vue';
import QuizAnswersView from '../panels/QuizAnswersView.vue';

const route = useRoute();
const dashboardStore = useQuizDashboardStore();

const tab = ref('users');
const currentAssessmentId = ref<number | null>(null);

// Computed properties
const isPageLoading = computed(() => {
  return (
    dashboardStore.isLoadingMeta ||
    dashboardStore.isLoadingScoreDistribution ||
    dashboardStore.isLoadingQuestions ||
    dashboardStore.isLoadingParticipants
  );
});

const hasAnyData = computed(() => {
  return (
    dashboardStore.assessmentMeta !== null ||
    dashboardStore.scoreDistribution !== null ||
    dashboardStore.questionResponses !== null ||
    dashboardStore.participants !== null
  );
});

// Helper functions
function parseAssessmentIdFromRoute(): number | null {
  const idParam = route.params.id;
  if (idParam === 'new') return null;

  if (typeof idParam === 'string') {
    const num = Number(idParam);
    return isNaN(num) ? null : num;
  } else if (Array.isArray(idParam) && typeof idParam[0] === 'string') {
    const num = Number(idParam[0]);
    return isNaN(num) ? null : num;
  }
  return null;
}

async function initializeDashboard(assessmentId: number | null) {
  console.log('[Dashboard] Initializing dashboard with assessmentId:', assessmentId);

  if (assessmentId === null) {
    console.log('[Dashboard] Assessment ID is null, resetting store');
    dashboardStore.reset();
    currentAssessmentId.value = null;
    return;
  }

  currentAssessmentId.value = assessmentId;

  try {
    // Fetch basic assessment metadata first
    await dashboardStore.fetchDashboardMeta(assessmentId);

    // Then fetch score distribution for dashboard view
    await dashboardStore.fetchScoreDistribution(assessmentId);

    console.log('[Dashboard] Basic data loaded successfully');
  } catch (error) {
    console.error('[Dashboard] Error loading initial data:', error);
  }
}

async function retryFetchAll() {
  console.log('[Dashboard] Retrying fetch all data');
  dashboardStore.clearError();

  if (currentAssessmentId.value !== null) {
    await initializeDashboard(currentAssessmentId.value);
  } else {
    const assessmentIdFromRoute = parseAssessmentIdFromRoute();
    await initializeDashboard(assessmentIdFromRoute);
  }
}

// Lifecycle
onMounted(async () => {
  console.log('[Dashboard] Component mounted, route params:', route.params);
  const assessmentIdFromRoute = parseAssessmentIdFromRoute();
  await initializeDashboard(assessmentIdFromRoute);
});

// Watchers
watch(
  () => route.params.id,
  async (newIdParam, oldIdParam) => {
    console.log('[Dashboard] Route param changed from', oldIdParam, 'to', newIdParam);
    if (newIdParam !== oldIdParam) {
      const newAssessmentId = parseAssessmentIdFromRoute();
      if (currentAssessmentId.value !== newAssessmentId) {
        await initializeDashboard(newAssessmentId);
      }
    }
  },
);

// Reset store when component unmounts
import { onUnmounted } from 'vue';
import { useQuizDashboardStore } from 'src/stores/quizdashboardStore';
onUnmounted(() => {
  console.log('[Dashboard] Component unmounted, resetting store');
  dashboardStore.reset();
});


</script>

<style scoped>
:deep(.q-tab .q-tab__label) {
  font-size: 1.1rem;
  font-weight: 500;
  text-transform: none;
  padding-top: 4px;
  padding-bottom: 4px;
}

.custom-light-shadow {
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.19);
}
</style>

<template>
  <div class="responsive-bar row">
    <div class="title-bar col-12 col-md">
      {{ title }}
    </div>
    <div class="response-bar col-12 col-md d-flex justify-content-center align-items-center">
      <div class="response-text">{{ responses }} การตอบ</div>
    </div>
    <div class="button-bar col d-flex justify-content-end align-items-center">
      <q-btn label="ดาวน์โหลด Excel" color="positive" @click="responsesService.getAssessmentToExcelById(props.id)" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { ResponsesService } from 'src/services/asm/responseService';
const title = ref('ชื่อแบบสอบถามมันจะยาวขนาดนี้ได้ยังไงวะเนี้ย นอกจากเราจะเป็นคนที่พิมพ์ยาวมากๆ');
const responses = ref(555);

const props = defineProps<{
  id: number;
}>();

const responsesService = new ResponsesService(); // ใช้บริการที่นำเข้า
const getResponseHeader = async () => {
  try {
    const res = await responsesService.getResponseHeaderById(props.id);
    title.value = res.data.name;
    responses.value = res.data.number;
  } catch (error) {
    console.error('Error fetching response header:', error);
  }
};

onMounted(async () => {
  await getResponseHeader();
});
</script>

<style scoped lang="scss">
.responsive-bar {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  background-color: white !important;
  color: black !important;
  border: 1px solid !important;
  border-radius: 21px;
  width: 100%;
  max-width: 1000px;

  min-height: 80px; /* ให้สูงอย่างน้อย 80px */
  height: auto; /* ให้ความสูงปรับตามเนื้อหา */

  padding: 0 20px;
  gap: 10px;
}

/* title-bar ให้กว้างและยืดหยุ่น */
.title-bar {
  flex: 1 1 40%; /* กินพื้นที่ 40% แต่ยืดหยุ่นได้ */
  font-size: medium;
  padding-left: 10px;

  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* response-bar */
.response-bar {
  flex: 0 1 20%; /* กินพื้นที่ 20% ยืดหยุ่นเล็กน้อย */
  display: flex;
  align-items: center;
  justify-content: center;
}

/* กรอบ response-text */
.response-text {
  background-color: $primary;
  color: white;
  border-radius: 8px;
  text-align: center;
  padding: 4px 12px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 150px;
  height: 40px;
}

/* button-bar */
.button-bar {
  flex: 0 1 30%; /* กินพื้นที่ 30% ยืดหยุ่น */
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding-right: 10px;
}

/* Responsive: Tablet (max-width 768px) */
@media (max-width: 768px) {
  .responsive-bar {
    height: auto;
    padding: 10px;
  }

  .title-bar,
  .response-bar,
  .button-bar {
    flex: 1 1 100%;
    text-align: center;
    padding: 5px 0;
  }

  .button-bar {
    justify-content: center;
    padding-right: 0;
  }
}

/* Responsive: Mobile (max-width 480px) */
@media (max-width: 480px) {
  .responsive-bar {
    flex-direction: column;
    height: auto;
    padding: 10px;
  }

  .title-bar,
  .response-bar,
  .button-bar {
    flex: 1 1 100%;
    text-align: center;
    padding: 5px 0;
  }
}
</style>

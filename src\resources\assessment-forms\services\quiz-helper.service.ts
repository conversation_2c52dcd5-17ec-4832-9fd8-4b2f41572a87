import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, <PERSON><PERSON>ty<PERSON>anager, Not, IsNull } from 'typeorm';
import { Assessment } from '../entities/assessment.entity';
import { Submission } from '../entities/submission.entity';
import { ItemBlock } from '../entities/item-block.entity';

export interface AttemptInfo {
  completedAttempts: number;
  maxAttempts: number;
  hasUnlimitedAttempts: boolean;
  canAttempt: boolean;
}

export interface ScoreCalculationResult {
  score: number;
  totalScore: number;
  passed: boolean;
}

export interface TimeFormatOptions {
  format: 'human' | 'hms'; // 'human' = "1 ชั่วโมง 30 นาที", 'hms' = "01:30:00"
}

@Injectable()
export class QuizHelperService {
  constructor(
    @InjectRepository(Assessment)
    private assessmentRepository: Repository<Assessment>,
    @InjectRepository(ItemBlock)
    private itemBlockRepository: Repository<ItemBlock>,
    @InjectRepository(Submission)
    private submissionRepository: Repository<Submission>,
  ) {}

  /**
   * Find assessment by linkUrl with error handling
   */
  async findAssessmentByLinkUrl(linkUrl: string): Promise<Assessment> {
    const assessment = await this.assessmentRepository.findOne({
      where: { linkURL: linkUrl },
    });

    if (!assessment) {
      throw new NotFoundException(
        `Assessment with linkUrl "${linkUrl}" not found`,
      );
    }
    // find itemblock header
    const itemBlockHeader = await this.itemBlockRepository.findOne({
      where: { assessmentId: assessment.id, headerBody: true },
      relations: { headerBody: true },
    });
    assessment.itemBlocks = [];
    assessment.itemBlocks.push(itemBlockHeader);

    return assessment;
  }

  /**
   * Count completed submissions for a user and assessment
   */
  async getCompletedSubmissionsCount(
    assessmentId: number,
    userId: number,
    entityManager?: EntityManager,
  ): Promise<number> {
    const manager = entityManager || this.submissionRepository.manager;

    return await manager.count(Submission, {
      where: {
        assessmentId,
        userId,
        submitAt: Not(IsNull()), // Count only completed submissions
      },
    });
  }

  /**
   * Get user's completed submissions with full details
   */
  async getUserCompletedSubmissions(
    assessmentId: number,
    userId: number,
  ): Promise<Submission[]> {
    return await this.submissionRepository.find({
      where: {
        userId,
        assessmentId,
        submitAt: Not(IsNull()),
      },
      relations: {
        responses: {
          selectedOption: true,
        },
      },
      order: {
        submitAt: 'DESC',
      },
    });
  }

  /**
   * Calculate attempt information for a user and assessment
   */
  async calculateAttemptInfo(
    assessment: Assessment,
    userId?: number,
    entityManager?: EntityManager,
  ): Promise<AttemptInfo> {
    const attemptInfo: AttemptInfo = {
      completedAttempts: 0,
      maxAttempts: assessment.submitLimit,
      hasUnlimitedAttempts: assessment.submitLimit <= 0,
      canAttempt: true,
    };

    if (userId) {
      attemptInfo.completedAttempts = await this.getCompletedSubmissionsCount(
        assessment.id,
        userId,
        entityManager,
      );

      attemptInfo.canAttempt =
        attemptInfo.hasUnlimitedAttempts ||
        attemptInfo.completedAttempts < attemptInfo.maxAttempts;
    }

    return attemptInfo;
  }

  /**
   * Calculate score from submission responses
   */
  calculateScore(
    submission: Submission,
    assessment: Assessment,
  ): ScoreCalculationResult {
    let score = 0;

    for (const response of submission.responses) {
      if (response.selectedOption) {
        score += response.selectedOption.value || 0;
      }
    }

    const passed = score >= assessment.totalScore * assessment.passRatio;

    return {
      score,
      totalScore: assessment.totalScore,
      passed,
    };
  }

  /**
   * Format time duration with configurable output format
   */
  formatTime(
    seconds: number,
    options: TimeFormatOptions = { format: 'human' },
  ): string {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = seconds % 60;

    if (options.format === 'hms') {
      // HH:MM:SS format
      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
    }

    // Human readable format (default)
    const parts = [];
    if (hours > 0) parts.push(`${hours} ชั่วโมง`);
    if (minutes > 0) parts.push(`${minutes} นาที`);
    if (remainingSeconds > 0) parts.push(`${remainingSeconds} วินาที`);

    return parts.length > 0 ? parts.join(' ') : '0 วินาที';
  }

  /**
   * Calculate time spent between two dates
   */
  calculateTimeSpent(startAt: Date, endAt: Date): number {
    const timeSpentMs = endAt.getTime() - startAt.getTime();
    return Math.floor(timeSpentMs / 1000);
  }

  /**
   * Validate if user can attempt assessment (used in startAssessment)
   */
  async validateUserCanAttempt(
    assessment: Assessment,
    userId: number,
    entityManager: EntityManager,
  ): Promise<void> {
    if (assessment.submitLimit > 0) {
      const completedAttempts = await this.getCompletedSubmissionsCount(
        assessment.id,
        userId,
        entityManager,
      );

      if (completedAttempts >= assessment.submitLimit) {
        throw new Error(
          `Assessment submission limit reached. You have already completed ${completedAttempts} out of ${assessment.submitLimit} allowed attempts.`,
        );
      }
    }
  }
}

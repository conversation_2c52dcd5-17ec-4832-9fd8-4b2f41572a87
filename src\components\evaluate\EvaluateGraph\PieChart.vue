<template>
  <div class="chart-wrapper">
    <canvas ref="canvas"></canvas>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, watch, defineExpose } from 'vue';
import { Chart, PieController, ArcElement, Toolt<PERSON>, Legend } from 'chart.js';
import ChartDataLabels from 'chartjs-plugin-datalabels';

Chart.register(PieController, ArcElement, Tooltip, Legend, ChartDataLabels);

const props = defineProps<{
  labels: string[];
  data: number[];
}>();

const canvas = ref<HTMLCanvasElement | null>(null);
let chartInstance: Chart | null = null;

const pieColors = ['#3d3c91', '#5961b3', '#7883cc', '#a0aee6', '#c8cfff'];

function renderChart() {
  if (!canvas.value) return;
  if (chartInstance) chartInstance.destroy();

  // คำนวณผลรวมของข้อมูลทั้งหมด
  const total = props.data.reduce((sum, value) => sum + value, 0);

  chartInstance = new Chart(canvas.value, {
    type: 'pie',
    data: {
      labels: props.labels,
      datasets: [
        {
          data: props.data,
          backgroundColor: pieColors.slice(0, props.data.length),
          borderColor: 'white',
          borderWidth: 2,
        },
      ],
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: 'right',
          align: 'center',
          labels: {
            usePointStyle: true,
            boxWidth: 20,
            padding: 30,
            font: {
              size: 20,
              weight: 'bold',
            },
          },
        },
        datalabels: {
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
            display: (context : any) => {
            return context.dataset.data[context.dataIndex] > 0; // แสดงเฉพาะเมื่อค่า > 0
          },
          color: 'white',
          anchor: 'center',
          align: 'center',
          formatter: (value: number) => {
            const percentage = total > 0 ? ((value / total) * 100).toFixed(1) : '0.0';
            return `${percentage}%`;
          },
          font: {
            size: 14,
            weight: 'bold',
          },
        },
      },
      layout: { padding: 0 },
    },
  });
}

onMounted(renderChart);

watch(
  () => [props.labels, props.data],
  () => {
    renderChart();
  },
);

onBeforeUnmount(() => {
  if (chartInstance) chartInstance.destroy();
});

defineExpose({ canvas });
</script>

<style scoped>
canvas {
  width: 100% !important;
  height: 300px !important;
}
</style>
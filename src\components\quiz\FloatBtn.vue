<template>
  <div>
    <q-btn
      class="float-btn-toggle"
      icon="list"
      color="primary"
      round
      @click="show = !show"
      style="position: fixed; top: 100px; right: 100px; z-index: 2001"
    />
    <transition name="fade">
      <div
        v-if="show"
        class="float-btn-floating"
        :style="{ maxWidth: '578px', maxHeight: '408px' }"
        ref="floatBtnRef"
      >
        <div
          class="question-grid"
          :style="{
            gridTemplateColumns: `repeat(${gridColumns}, 1fr)`,
            gridTemplateRows: `repeat(${gridRows}, 1fr)`,
            maxWidth: '546px',
            maxHeight: '370px',
            overflowY: gridRows > maxRows ? 'auto' : 'unset',
            overflowX: 'hidden',
            height: gridRows > maxRows ? '370px' : 'auto',
            width:
              gridColumns < maxColumns ? `${gridColumns * 62 + (gridColumns - 1) * 6}px` : '100%',
            justifyContent: gridColumns < maxColumns ? 'center' : 'unset',
          }"
        >
          <q-btn
            v-for="n in totalQuestions"
            :key="n"
            :label="n.toString()"
            :color="
              n === currentQuestion
                ? 'primary'
                : doneList && doneList[n - 1]
                  ? 'positive'
                  : 'grey-8'
            "
            class="question-btn"
            :outline="n !== currentQuestion"
            :unelevated="n === currentQuestion"
            @click="() => emit('select', n)"
          />
        </div>
      </div>
    </transition>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits, ref, computed, onMounted, onBeforeUnmount } from 'vue';

const props = defineProps<{
  totalQuestions: number;
  currentQuestion?: number;
  doneList?: boolean[];
}>();
const emit = defineEmits(['select']);
const show = ref(true);
const floatBtnRef = ref<HTMLElement | null>(null);

const maxColumns = 8;
const maxRows = 5;
const gridColumns = computed(() => {
  return Math.min(maxColumns, props.totalQuestions);
});
const gridRows = computed(() => {
  return Math.ceil(props.totalQuestions / gridColumns.value) || 1;
});

function handleClickOutside(event: MouseEvent) {
  if (!show.value) return;
  const el = floatBtnRef.value;
  // Prevent closing when clicking inside the floating button or on any grid/question button
  if (el && el.contains(event.target as Node)) {
    return;
  }
  show.value = false;
}

onMounted(() => {
  document.addEventListener('mousedown', handleClickOutside);
});
onBeforeUnmount(() => {
  document.removeEventListener('mousedown', handleClickOutside);
});
</script>

<style scoped>
.float-btn-floating {
  position: fixed;
  top: 152px;
  right: 100px;
  z-index: 2000;
  background: #fff;
  border-radius: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.18);
  padding: 16px 16px 8px 16px;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  transition: box-shadow 0.2s;
  max-width: 578px;
  max-height: 408px;
}
.float-btn-header {
  width: 100%;
  display: flex;
  justify-content: flex-end;
  margin-bottom: 4px;
}
.close-btn {
  margin-bottom: 4px;
}
.question-grid {
  display: grid;
  gap: 6px;
  margin: 0 auto;
  max-width: 546px;
  max-height: 370px;
  overflow-x: hidden;
  justify-content: center;
}
.question-btn {
  width: 100%;
  height: 100%;
  min-width: 44px;
  min-height: 44px;
  font-weight: bold;
  font-size: 1.1em;
  padding: 0;
  transition: background 0.15s;
}
.float-btn-toggle {
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.18);
  position: fixed;
  top: 32px;
  right: 32px;
  z-index: 2001;
  width: 54px;
  height: 54px;
  font-size: 1.5em;
}
.float-btn-toggle-inside {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.18);
  position: absolute;
  top: -44px;
  right: 0;
  z-index: 2002;
}
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.2s;
}
.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>

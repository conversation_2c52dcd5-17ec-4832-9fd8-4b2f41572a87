import type { QTableProps } from 'quasar';
import type { DataParams } from 'src/types/data';

export function formatParams(pag: QTableProps['pagination'], search?: string) {
  const params: DataParams = {
    sortBy: pag?.sortBy || null,
    order: 'DESC',
    limit: pag?.rowsPerPage || 10,
    page: pag?.page || 1,
    search: search || null,
  };
  return params;
}

export function formatDateDisplay(dateString?: string): string {
  if (!dateString) return '-';
  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) {
      const parts = dateString.split('/');
      if (parts.length === 3 && parts[0] && parts[1] && parts[2]) return dateString;
      return dateString;
    }
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const year = date.getFullYear() + 543; // พ.ศ.
    return `${day}/${month}/${year}`;
  } catch (e) {
    console.error('Error formatting date:', dateString, e);
    return dateString;
  }
}

export const formatSeconds = (seconds: number) => {
  const min = Math.floor(seconds / 60);
  const sec = seconds % 60;
  return `${String(min).padStart(2, '0')}.${String(sec).padStart(2, '0')}`;
};

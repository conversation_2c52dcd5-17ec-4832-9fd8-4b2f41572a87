import { ApiService } from './../../../api/api.service';
import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CreateAssessmentDto } from '../dto/creates/create-assessment.dto';
import { UpdateAssessmentDto } from '../dto/updates/update-assessment.dto';
import { Assessment } from '../entities/assessment.entity';
import type { DataParams, DataResponse } from 'src/types/params';
import { AssessmentType } from '../enums/assessment-type.enum';
import { v4 as uuidv4 } from 'uuid';
import { AssessmentConfig } from 'src/configs/assessment.config';
import { ItemBlocksService } from '../item-blocks/item-blocks.service';
import type { User } from 'src/resources/users/entities/user.entity';
import type { Program } from 'src/resources/programs/entities/program.entity';
import { HeaderBodiesService } from '../header-bodies/header-bodies.service';
import { QuestionsService } from '../questions/questions.service';
import { OptionsService } from '../options/options.service';
import { ImageBodiesService } from '../image-bodies/image-bodies.service';


@Injectable()
export class AssessmentsService {
  constructor(
    @InjectRepository(Assessment)
    private assessmentRepository: Repository<Assessment>,
    private itemBlocksService: ItemBlocksService,
    private headerBodiesService: HeaderBodiesService,
    private readonly questionService: QuestionsService,
    private readonly optionsService: OptionsService,
    private readonly imageBodiesService: ImageBodiesService,
    private readonly apiService: ApiService,
  ) {}

  async createOne(dto: CreateAssessmentDto): Promise<Assessment> {
    const { creatorUserId, programId, type } = dto;

    if (!creatorUserId || !programId || !type) {
      throw new BadRequestException(
        'creatorUserId, programId, and type are required',
      );
    }

    const saved = await this.assessmentRepository.save({
      name: AssessmentConfig.FORM_UNTITLED,
      type,
      linkURL: uuidv4(),
      creator: { id: creatorUserId } as User,
      program: { id: programId } as Program,
    });

    // const firstItemBlock = await this.itemBlocksService
    //   .createEmptyBlock({
    //     sequence: 1,
    //     section: 1,
    //     type: ItemBlockType.HEADER,
    //     assessmentId: saved.id,
    //   })
    //   .then(async (block) => {
    //     await this.headerBodiesService.createHeaderBody(block.id);
    //     return block;
    //   });

    // Return all required Assessment properties, filling missing ones with null or empty as needed
    return {
      ...saved,
      creator: saved.creator, // likely just id
      program: saved.program, // likely just id
      submissions: [], // default empty array
      isPrototype: saved.isPrototype ?? false, // fallback if not present
    } as Assessment;
  }

  // Generic getAll for dashboard (with type)
  async getAll(
    query: DataParams,
    type: AssessmentType,
  ): Promise<DataResponse<Assessment>> {
    const { sortBy, order, search } = query;
    const page = query.page ?? 10;
    const limit = query.limit ?? 1;

    const qb = this.assessmentRepository
      .createQueryBuilder('assessment')
      .leftJoinAndSelect('assessment.creator', 'creator')
      .where('assessment.type = :type', { type });
    if (search) {
      qb.andWhere('LOWER(assessment.name) LIKE LOWER(:search)', {
        search: `%${search}%`,
      });
    }
    if (sortBy) {
      qb.orderBy(
        `assessment.${sortBy}`,
        (order || 'ASC').toUpperCase() as 'ASC' | 'DESC',
      );
    } else {
      qb.orderBy('assessment.id', 'ASC');
    }

    qb.skip((page - 1) * limit).take(limit);
    const [data, total] = await qb.getManyAndCount();
    const totalPages = Math.ceil(total / limit);
    return {
      data,
      total,
      curPage: page,
      hasPrev: page > 1,
      hasNext: page < totalPages,
    };
  }

  // Get ALl Editor Roles
  async getAllEditor(
    query: DataParams,
    type: AssessmentType,
    user: any, // Replace 'any' with your user interface/type
  ): Promise<DataResponse<Assessment>> {
    const { sortBy, order, search } = query;
    const page = query.page ?? 1;
    const limit = query.limit ?? 10;

    const qb = this.assessmentRepository
      .createQueryBuilder('assessment')
      .leftJoinAndSelect('assessment.creator', 'creator')
      .where('assessment.type = :type', { type })
      .andWhere('assessment.creator = :creatorId', { creatorId: user.id }); // Filter by user

    if (search) {
      qb.andWhere('LOWER(assessment.name) LIKE LOWER(:search)', {
        search: `%${search}%`,
      });
    }
    if (sortBy) {
      qb.orderBy(
        `assessment.${sortBy}`,
        (order || 'ASC').toUpperCase() as 'ASC' | 'DESC',
      );
    } else {
      qb.orderBy('assessment.id', 'ASC');
    }

    qb.skip((page - 1) * limit).take(limit);
    const [data, total] = await qb.getManyAndCount();
    const totalPages = Math.ceil(total / limit);
    return {
      data,
      total,
      curPage: page,
      hasPrev: page > 1,
      hasNext: page < totalPages,
    };
  }

  // Get All Prototype Assessments
  async getAllPrototypes(
    type: AssessmentType,
    query: DataParams,
  ): Promise<DataResponse<Assessment>> {
    const { sortBy, order, search } = query;
    const page = query.page ?? 1;
    const limit = query.limit ?? 10;

    const qb = this.assessmentRepository
      .createQueryBuilder('assessment')
      .leftJoinAndSelect('assessment.creator', 'creator')
      .where('assessment.type = :type', { type })
      .andWhere('assessment.isPrototype = :isPrototype', { isPrototype: true });

    if (search) {
      qb.andWhere('LOWER(assessment.name) LIKE LOWER(:search)', {
        search: `%${search}%`,
      });
    }

    if (sortBy) {
      qb.orderBy(
        `assessment.${sortBy}`,
        (order || 'ASC').toUpperCase() as 'ASC' | 'DESC',
      );
    } else {
      qb.orderBy('assessment.id', 'ASC');
    }

    qb.skip((page - 1) * limit).take(limit);

    const [data, total] = await qb.getManyAndCount();
    const totalPages = Math.ceil(total / limit);

    return {
      data,
      total,
      curPage: page,
      hasPrev: page > 1,
      hasNext: page < totalPages,
    };
  }

  // Find one (with all relations)
  async findOne(id: number): Promise<Assessment> {
    const assessment = await this.assessmentRepository.findOne({
      where: { id },
      relations: [
        'creator',
        'program',
        'itemBlocks',
        'itemBlocks.questions',
        'itemBlocks.options',
        'itemBlocks.headerBody',
        'itemBlocks.imageBody',
      ],
    });

    if (!assessment) {
      throw new NotFoundException(`Assessment with ID "${id}" not found`);
    }

    await this.updateImagePaths(assessment);

    return assessment;
  }

  // Find one by section
  async findOneBySection(id: number, section: number) {
    if (!id || isNaN(id)) {
      throw new BadRequestException('Invalid assessment ID');
    }

    const assessment = await this.assessmentRepository
      .createQueryBuilder('assessment')
      .leftJoinAndSelect('assessment.itemBlocks', 'itemBlocks')
      .leftJoinAndSelect('itemBlocks.questions', 'questions')
      .leftJoinAndSelect('itemBlocks.imageBody', 'imageBody')
      .leftJoinAndSelect('itemBlocks.options', 'options')
      .leftJoinAndSelect('itemBlocks.headerBody', 'headerBody')
      .where('assessment.id = :id', { id })
      .andWhere('assessment.type = :type', { type: AssessmentType.EVALUATE })
      .andWhere('itemBlocks.section = :section', { section })
      .getOne();

    if (assessment) {
      await this.updateImagePaths(assessment);
    }

    return assessment;
  }

  async getAllUser(
    query: DataParams,
    type: AssessmentType,
  ): Promise<DataResponse<Assessment>> {
    const { sortBy, order, search } = query;
    const page = query.page ?? 1;
    const limit = query.limit ?? 10;

    const qb = this.assessmentRepository
      .createQueryBuilder('assessment')
      .leftJoinAndSelect('assessment.creator', 'creator')
      .where('assessment.type = :type', { type })
      .andWhere('assessment.endAt >= :currentDate', {
      currentDate: new Date(),
      }) // Added condition
      .andWhere('assessment.status = :status', { status: true }); // Check status = true

    if (search) {
      qb.andWhere('LOWER(assessment.name) LIKE LOWER(:search)', {
        search: `%${search}%`,
      });
    }
    if (sortBy) {
      qb.orderBy(
        `assessment.${sortBy}`,
        (order || 'ASC').toUpperCase() as 'ASC' | 'DESC',
      );
    } else {
      qb.orderBy('assessment.id', 'ASC');
    }

    qb.skip((page - 1) * limit).take(limit);
    const [data, total] = await qb.getManyAndCount();
    const totalPages = Math.ceil(total / limit);
    return {
      data,
      total,
      curPage: page,
      hasPrev: page > 1,
      hasNext: page < totalPages,
    };
  }

  // Update
  async update(
    id: number,
    updateAssessmentDto: UpdateAssessmentDto,
  ): Promise<Assessment> {
    if (!updateAssessmentDto || Object.keys(updateAssessmentDto).length === 0) {
      throw new BadRequestException('No update values provided');
    }

    const assessment = await this.findOne(id);
    // console.log('Received DTO:', updateAssessmentDto);
    // console.log('Before update:', assessment);

    const fieldMappings: {
      [key in keyof UpdateAssessmentDto]?: keyof Assessment;
    } = {
      name: 'name',
      submitLimit: 'submitLimit',
      status: 'status',
      startAt: 'startAt',
      endAt: 'endAt',
      timeout: 'timeout',
      isPrototype: 'isPrototype',
      passRatio: 'passRatio',
      totalScore: 'totalScore',
      responseEdit: 'responseEdit',
    };

    const booleanFields: (keyof Assessment)[] = [
      'status',
      'isPrototype',
      'responseEdit',
    ];

    for (const key in updateAssessmentDto) {
      const dtoKey = key as keyof UpdateAssessmentDto;
      if (fieldMappings[dtoKey] && updateAssessmentDto[dtoKey] !== undefined) {
        const entityKey = fieldMappings[dtoKey]!;
        let value = updateAssessmentDto[dtoKey];
        // Fallback for boolean fields
        if (booleanFields.includes(entityKey)) {
          //Transform string to True boolean
          value =
            typeof value === 'string'
              ? value.toLowerCase() === 'true'
              : Boolean(value);
        }
        console.log(`Setting ${entityKey}:`, value, 'Type:', typeof value);
        (assessment[entityKey] as any) = value;
      }
    }

    // console.log('Updated assessment:', assessment);
    const updatedAssessment = await this.assessmentRepository.save(assessment);
    // console.log('Saved to DB:', updatedAssessment);

    return updatedAssessment;
  }

  // Remove (simple, for dashboard)
  async remove(id: number) {
    const assessment = await this.assessmentRepository.findOne({
      where: { id },
    });
    if (!assessment) {
      throw new NotFoundException('Assessment not found');
    }
    await this.assessmentRepository.remove(assessment);
    return { success: true };
  }

  async duplicateAssessment(
    sourceId: number,
    targetId: number,
  ): Promise<Assessment> {
    const source = await this.assessmentRepository.findOne({
      where: { id: sourceId },
      relations: [
        'itemBlocks',
        'itemBlocks.questions',
        'itemBlocks.options',
        'itemBlocks.headerBody',
        'itemBlocks.imageBody',
      ],
    });

    const target = await this.assessmentRepository.findOne({
      where: { id: targetId },
    });

    if (!source || !target) {
      throw new NotFoundException('Source or Target Assessment not found');
    }

    // 1. ลบ itemBlocks เดิม (และข้อมูลลูก) ทั้งหมดของ targetId
    await this.itemBlocksService.removeAllByAssessmentId(targetId); // คุณต้องมีฟังก์ชันนี้ใน itemBlocksService

    // 2. อัปเดตข้อมูลของ assessment เป้าหมาย
    await this.assessmentRepository.update(targetId, {
      name: `${source.name} (สำเนา)`,
      type: source.type,
      linkURL: uuidv4(),
      responseEdit: source.responseEdit,
      status: false,
      totalScore: source.totalScore,
      timeout: source.timeout,
      passRatio: source.passRatio,
      submitLimit: source.submitLimit,
      isPrototype: source.isPrototype,
      startAt: null,
      endAt: null,
    });

    // 3. คัดลอก itemBlocks + children ทั้งหมดจาก source มาสร้างใน target
    for (const oldBlock of source.itemBlocks) {
      const newBlock = await this.itemBlocksService.createBlock({
        assessmentId: targetId,
        sequence: oldBlock.sequence,
        section: oldBlock.section,
        type: oldBlock.type,
        isRequired: oldBlock.isRequired,
      });

      // header body
      if (oldBlock.headerBody) {
        // ลบของเดิมก่อน (ถ้ามี)
        await this.headerBodiesService.deleteByItemBlockId(newBlock.id);

        // แล้วค่อยสร้างใหม่
        await this.headerBodiesService.create({
          itemBlockId: newBlock.id,
          title: oldBlock.headerBody.title,
          description: oldBlock.headerBody.description,
        });
      }

      // image body
      if (oldBlock.imageBody) {
        await this.imageBodiesService.deleteByItemBlockId(newBlock.id);

        await this.imageBodiesService.create({
          itemBlockId: newBlock.id,
          imageText: oldBlock.imageBody.imageText,
          imagePath: oldBlock.imageBody.imagePath || null,
          imageWidth: oldBlock.imageBody.imageWidth,
          imageHeight: oldBlock.imageBody.imageHeight,
        });
      }

      // questions
      for (const question of oldBlock.questions) {
        await this.questionService.create({
          itemBlockId: newBlock.id,
          questionText: question.questionText,
          imagePath: question.imagePath || null,
          isHeader: question.isHeader,
          sequence: question.sequence,
          sizeLimit: question.sizeLimit,
          acceptFile: question.acceptFile,
          uploadLimit: question.uploadLimit,
          score: question.score,
        });
      }

      // options
      for (const option of oldBlock.options) {
        await this.optionsService.create({
          itemBlockId: newBlock.id,
          optionText: option.optionText,
          imagePath: option.imagePath || null,
          value: option.value,
          sequence: option.sequence,
          nextSection: option.nextSection,
        });
      }
    }

    // 4. Return assessment ที่อัปเดตแล้ว
    return await this.assessmentRepository.findOne({
      where: { id: targetId },
      relations: [
        'itemBlocks',
        'itemBlocks.questions',
        'itemBlocks.options',
        'itemBlocks.headerBody',
        'itemBlocks.imageBody',
      ],
    });
  }

  // Dashboard summary (example)
  async getDashboard(type: AssessmentType) {
    const total = await this.assessmentRepository.count({ where: { type } });
    return { total };
  }

  // Private helper method to update image paths
  private async updateImagePaths(assessment: Assessment): Promise<void> {
    await Promise.all(
      assessment.itemBlocks.map(async (itemBlock) => {
        const updateImagePath = async (filePath: string | null | undefined) => {
          if (filePath) {
            try {
              const result = await this.apiService.getPublicFile(filePath);
              return result?.result?.file_1?.view || filePath;
            } catch (error) {
              console.error(`Error fetching file ${filePath}:`, error);
              return filePath;
            }
          }
          return filePath;
        };

        if (itemBlock.imageBody?.imagePath) {
          itemBlock.imageBody.imagePath = await updateImagePath(
            itemBlock.imageBody.imagePath,
          );
        }

        if (itemBlock.options?.length) {
          await Promise.all(
            itemBlock.options.map(async (option) => {
              option.imagePath = await updateImagePath(option.imagePath);
            }),
          );
        }

        if (itemBlock.questions?.length) {
          await Promise.all(
            itemBlock.questions.map(async (question) => {
              question.imagePath = await updateImagePath(question.imagePath);
            }),
          );
        }
      }),
    );
  }
  async getByURL(uuid: string): Promise<Assessment> {
    const assessment = await this.assessmentRepository.findOne({
      where: { linkURL: uuid },
      relations: [
        'creator',
        'program',
        'itemBlocks',
        'itemBlocks.questions',
        'itemBlocks.options',
        'itemBlocks.headerBody',
        'itemBlocks.imageBody',
      ],
    });

    if (!assessment) {
      throw new NotFoundException(
        `Assessment with linkURL "${uuid}" not found`,
      );
    }

    await this.updateImagePaths(assessment);

    return assessment;
  }
}
